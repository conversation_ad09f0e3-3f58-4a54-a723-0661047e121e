import React, { useState, useEffect } from "react";

// Main App Component
const App = () => {
  const [currentScreen, setCurrentScreen] = useState("Welcome"); // State for screen navigation

  // Simulate Firebase Auth and Firestore setup
  const [db, setDb] = useState(null);
  const [auth, setAuth] = useState(null);
  const [userId, setUserId] = useState(null);
  const [isAuthReady, setIsAuthReady] = useState(false);

  // Global variables from Canvas environment
  const appId = typeof __app_id !== "undefined" ? __app_id : "default-app-id";
  const firebaseConfig =
    typeof __firebase_config !== "undefined"
      ? JSON.parse(__firebase_config)
      : {};
  const initialAuthToken =
    typeof __initial_auth_token !== "undefined" ? __initial_auth_token : null;

  useEffect(() => {
    // Firebase initialization (simulated)
    const initializeFirebase = async () => {
      try {
        // Mock Firebase imports for React environment
        const firebase = await import("firebase/app");
        const firestore = await import("firebase/firestore");
        const firebaseAuth = await import("firebase/auth");

        const app = firebase.initializeApp(firebaseConfig);
        const firestoreDb = firestore.getFirestore(app);
        const authInstance = firebaseAuth.getAuth(app);

        setDb(firestoreDb);
        setAuth(authInstance);

        // Sign in with custom token or anonymously
        if (initialAuthToken) {
          await firebaseAuth.signInWithCustomToken(
            authInstance,
            initialAuthToken
          );
        } else {
          await firebaseAuth.signInAnonymously(authInstance);
        }

        // Listen for auth state changes
        firebaseAuth.onAuthStateChanged(authInstance, (user) => {
          if (user) {
            setUserId(user.uid);
          } else {
            setUserId(null); // User signed out or anonymous
          }
          setIsAuthReady(true); // Auth state is ready
        });
      } catch (error) {
        console.error("Error initializing Firebase:", error);
        setIsAuthReady(true); // Still set to true to unblock UI
      }
    };

    initializeFirebase();
  }, []); // Run once on component mount

  // Function to navigate between screens
  const navigateTo = (screenName) => {
    setCurrentScreen(screenName);
  };

  // Common Header Component
  const Header = ({ title, showBackButton = false, onBack }) => (
    <div className="flex items-center justify-between w-full p-4 bg-white border-b border-gray-200 rounded-t-2xl">
      {showBackButton ? (
        <button onClick={onBack} className="text-pink-600 text-xl">
          &larr;
        </button>
      ) : (
        <div className="w-6"></div> // Placeholder for alignment
      )}
      <div className="flex items-center">
        {/* Updated logo to ZakMakelaar */}
        <img
          src="https://placehold.co/30x30/f72585/ffffff?text=ZM"
          alt="ZakMakelaar Logo"
          className="h-8 w-8 mr-2 rounded-md"
        />
        <span className="text-xl font-bold text-gray-800">ZakMakelaar</span>
      </div>
      <div className="w-6"></div> {/* Placeholder for alignment */}
    </div>
  );

  // Screen Components
  const WelcomeScreen = () => (
    <div className="flex flex-col items-center justify-center h-full p-8 bg-white rounded-2xl text-center">
      <img
        src="https://placehold.co/150x150/f72585/ffffff?text=ZM"
        alt="ZakMakelaar Logo"
        className="mb-8 rounded-full"
      />
      <h1 className="text-4xl font-bold text-gray-800 mb-4">
        Welcome to ZakMakelaar!
      </h1>
      <p className="text-gray-600 text-lg mb-12">
        Your AI-powered rental assistant for the Dutch market.
      </p>
      <button
        onClick={() => navigateTo("LoginSignUp")}
        className="w-full bg-pink-600 text-white py-3 px-6 rounded-xl font-semibold text-lg hover:bg-pink-700 transition duration-300"
      >
        Get Started
      </button>
    </div>
  );

  const LoginSignUpScreen = () => {
    const [isLogin, setIsLogin] = useState(true);
    const [email, setEmail] = useState("");
    const [password, setPassword] = useState("");

    const handleAuth = (e) => {
      e.preventDefault();
      // Simulate authentication
      console.log(`Attempting to ${isLogin ? "log in" : "sign up"} with:`, {
        email,
        password,
      });
      // In a real app, you'd call Firebase auth methods here
      navigateTo("PreferencesSetup"); // Navigate on simulated success
    };

    return (
      <div className="flex flex-col items-center h-full p-8 bg-white rounded-2xl text-center">
        <Header
          title="Login/Sign Up"
          showBackButton={true}
          onBack={() => navigateTo("Welcome")}
        />
        <div className="flex items-center mb-6 mt-4">
          {/* Updated logo to ZakMakelaar */}
          <img
            src="https://placehold.co/40x40/f72585/ffffff?text=ZM"
            alt="ZakMakelaar Logo"
            className="h-10 w-10 mr-2 rounded-lg"
          />
          <span className="text-2xl font-bold text-gray-800">ZakMakelaar</span>
        </div>

        <h1 className="text-3xl font-semibold text-gray-800 mb-4">
          Nice to see you!
        </h1>
        <p className="text-gray-600 mb-8 leading-relaxed">
          {isLogin
            ? "Log in and discover the galactically good possibilities"
            : "Create an account to unlock all features"}
        </p>

        <form className="w-full" onSubmit={handleAuth}>
          <div className="mb-4 text-left">
            <label
              htmlFor="email"
              className="block text-sm font-medium text-gray-700 mb-2"
            >
              E-Mail
            </label>
            <input
              type="email"
              id="email"
              className="w-full p-3 mb-2 border border-gray-200 rounded-xl font-inter text-gray-700 focus:border-pink-600 outline-none"
              placeholder="<EMAIL>"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
            />
          </div>

          <div className="mb-4 text-left">
            <label
              htmlFor="password"
              className="block text-sm font-medium text-gray-700 mb-2"
            >
              Password
            </label>
            <input
              type="password"
              id="password"
              className="w-full p-3 mb-2 border border-gray-200 rounded-xl font-inter text-gray-700 focus:border-pink-600 outline-none"
              placeholder="************"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
            />
            {!isLogin && (
              <p className="text-xs text-gray-500 mt-1">
                Password must be at least 6 characters.
              </p>
            )}
            {isLogin && (
              <a
                href="#"
                className="block text-sm text-gray-500 hover:text-gray-700 mt-2 text-right"
              >
                Forgot password?
              </a>
            )}
          </div>

          <button
            type="submit"
            className="w-full bg-pink-600 text-white py-3 px-6 rounded-xl font-semibold text-lg hover:bg-pink-700 transition duration-300 mt-6 mb-6 flex items-center justify-center"
          >
            {isLogin ? "Continue" : "Sign Up"}{" "}
            <span className="ml-2">&rarr;</span>
          </button>
        </form>

        <div className="relative w-full mb-6">
          <div className="absolute inset-0 flex items-center">
            <div className="w-full border-t border-gray-300"></div>
          </div>
          <div className="relative flex justify-center text-sm">
            <span className="bg-white px-2 text-gray-500">
              Or {isLogin ? "log in" : "sign up"} with
            </span>
          </div>
        </div>

        <div className="flex space-x-4 w-full mb-8">
          <button className="flex-1 bg-gray-100 text-gray-700 py-3 px-4 rounded-xl font-semibold text-lg border border-gray-200 hover:bg-gray-200 transition duration-300 flex items-center justify-center">
            <i className="fab fa-google mr-2 text-red-500"></i> Google
          </button>
        </div>

        <p className="text-gray-600">
          {isLogin ? "Don't have an account yet?" : "Already have an account?"}{" "}
          <button
            onClick={() => setIsLogin(!isLogin)}
            className="text-pink-600 font-medium hover:underline"
          >
            {isLogin ? "Create one here!" : "Log in here!"}
          </button>
        </p>
      </div>
    );
  };

  const PreferencesSetupScreen = () => {
    const [location, setLocation] = useState("");
    const [rentRange, setRentRange] = useState("500-1500");
    const [bedrooms, setBedrooms] = useState("1");
    const [propertyType, setPropertyType] = useState("Apartment");

    const handleSavePreferences = () => {
      // Simulate saving preferences to Firestore
      if (db && userId && isAuthReady) {
        const docRef = db
          .collection(`artifacts/${appId}/users/${userId}/preferences`)
          .doc("rental");
        firestore
          .setDoc(docRef, {
            location,
            rentRange,
            bedrooms,
            propertyType,
            timestamp: firestore.serverTimestamp(),
          })
          .then(() => {
            console.log("Preferences saved successfully!");
            navigateTo("Dashboard");
          })
          .catch((error) => {
            console.error("Error saving preferences:", error);
          });
      } else {
        console.log(
          "Firebase not ready or user not authenticated. Simulating save:",
          { location, rentRange, bedrooms, propertyType }
        );
        navigateTo("Dashboard"); // Navigate even if Firebase is not ready
      }
    };

    return (
      <div className="flex flex-col items-center h-full p-8 bg-white rounded-2xl text-center">
        <Header
          title="Set Your Preferences"
          showBackButton={true}
          onBack={() => navigateTo("LoginSignUp")}
        />
        <h1 className="text-3xl font-semibold text-gray-800 mb-4 mt-4">
          Tell us what you're looking for!
        </h1>
        <p className="text-gray-600 mb-8 leading-relaxed">
          Help our AI find your perfect rental home.
        </p>

        <div className="w-full text-left space-y-6">
          <div>
            <label
              htmlFor="location"
              className="block text-sm font-medium text-gray-700 mb-2"
            >
              Preferred Location
            </label>
            <input
              type="text"
              id="location"
              className="w-full p-3 border border-gray-200 rounded-xl font-inter text-gray-700 focus:border-pink-600 outline-none"
              placeholder="e.g., Amsterdam, Utrecht"
              value={location}
              onChange={(e) => setLocation(e.target.value)}
            />
          </div>

          <div>
            <label
              htmlFor="rentRange"
              className="block text-sm font-medium text-gray-700 mb-2"
            >
              Monthly Rent Range
            </label>
            <select
              id="rentRange"
              className="w-full p-3 border border-gray-200 rounded-xl font-inter text-gray-700 focus:border-pink-600 outline-none bg-white"
              value={rentRange}
              onChange={(e) => setRentRange(e.target.value)}
            >
              <option value="0-500">€0 - €500</option>
              <option value="500-1000">€500 - €1000</option>
              <option value="1000-1500">€1000 - €1500</option>
              <option value="1500-2000">€1500 - €2000</option>
              <option value="2000+">€2000+</option>
            </select>
          </div>

          <div>
            <label
              htmlFor="bedrooms"
              className="block text-sm font-medium text-gray-700 mb-2"
            >
              Number of Bedrooms
            </label>
            <select
              id="bedrooms"
              className="w-full p-3 border border-gray-200 rounded-xl font-inter text-gray-700 focus:border-pink-600 outline-none bg-white"
              value={bedrooms}
              onChange={(e) => setBedrooms(e.target.value)}
            >
              <option value="studio">Studio</option>
              <option value="1">1</option>
              <option value="2">2</option>
              <option value="3">3</option>
              <option value="4+">4+</option>
            </select>
          </div>

          <div>
            <label
              htmlFor="propertyType"
              className="block text-sm font-medium text-gray-700 mb-2"
            >
              Property Type
            </label>
            <select
              id="propertyType"
              className="w-full p-3 border border-gray-200 rounded-xl font-inter text-gray-700 focus:border-pink-600 outline-none bg-white"
              value={propertyType}
              onChange={(e) => setPropertyType(e.target.value)}
            >
              <option value="Apartment">Apartment</option>
              <option value="House">House</option>
              <option value="Room">Room</option>
            </select>
          </div>
        </div>

        <button
          onClick={handleSavePreferences}
          className="w-full bg-pink-600 text-white py-3 px-6 rounded-xl font-semibold text-lg hover:bg-pink-700 transition duration-300 mt-10"
        >
          Save Preferences & Find Homes
        </button>
      </div>
    );
  };

  const DashboardScreen = () => {
    // Mock listings data
    const listings = [
      {
        id: 1,
        title: "Spacious Apartment in Amsterdam",
        price: "€1,800/month",
        location: "Amsterdam-Zuid",
        bedrooms: 2,
        imageUrl: "https://placehold.co/300x200/e0e0e0/333333?text=Apartment+1",
      },
      {
        id: 2,
        title: "Cozy Studio in Utrecht",
        price: "€950/month",
        location: "Utrecht Centrum",
        bedrooms: 1,
        imageUrl: "https://placehold.co/300x200/d0d0d0/333333?text=Apartment+2",
      },
      {
        id: 3,
        title: "Family House in Rotterdam",
        price: "€2,500/month",
        location: "Rotterdam Hillegersberg",
        bedrooms: 3,
        imageUrl: "https://placehold.co/300x200/c0c0c0/333333?text=House+1",
      },
    ];

    return (
      <div className="flex flex-col h-full bg-white rounded-2xl">
        <Header title="Your Dashboard" />
        <div className="p-6 flex-grow overflow-y-auto">
          <h1 className="text-3xl font-semibold text-gray-800 mb-6">
            Your Matches
          </h1>

          {/* Notifications Section */}
          <div className="bg-blue-50 border border-blue-200 text-blue-800 p-4 rounded-xl mb-6 flex items-center">
            <i className="fas fa-bell mr-3 text-blue-600"></i>
            <p className="text-sm">
              New listing matching your criteria in Amsterdam!
            </p>
          </div>

          {/* Listings Grid */}
          <div className="grid grid-cols-1 gap-6">
            {listings.map((listing) => (
              <div
                key={listing.id}
                className="bg-gray-50 rounded-xl shadow-sm overflow-hidden border border-gray-100 cursor-pointer hover:shadow-md transition duration-200"
                onClick={() => navigateTo("ListingDetails")}
              >
                <img
                  src={listing.imageUrl}
                  alt={listing.title}
                  className="w-full h-40 object-cover"
                />
                <div className="p-4 text-left">
                  <h2 className="text-lg font-semibold text-gray-800">
                    {listing.title}
                  </h2>
                  <p className="text-pink-600 font-bold text-xl mt-1">
                    {listing.price}
                  </p>
                  <p className="text-gray-600 text-sm mt-2">
                    {listing.location} &bull; {listing.bedrooms} bedrooms
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Bottom Navigation */}
        <div className="flex justify-around p-4 bg-white border-t border-gray-200 rounded-b-2xl">
          <button
            onClick={() => navigateTo("Dashboard")}
            className="flex flex-col items-center text-pink-600"
          >
            <i className="fas fa-home text-xl"></i>
            <span className="text-xs mt-1">Home</span>
          </button>
          <button
            onClick={() => navigateTo("Application")}
            className="flex flex-col items-center text-gray-500 hover:text-pink-600"
          >
            <i className="fas fa-file-alt text-xl"></i>
            <span className="text-xs mt-1">Apply</span>
          </button>
          <button
            onClick={() => navigateTo("ContractReview")}
            className="flex flex-col items-center text-gray-500 hover:text-pink-600"
          >
            <i className="fas fa-gavel text-xl"></i>
            <span className="text-xs mt-1">Contract</span>
          </button>
          <button className="flex flex-col items-center text-gray-500 hover:text-pink-600">
            <i className="fas fa-user text-xl"></i>
            <span className="text-xs mt-1">Profile</span>
          </button>
        </div>
      </div>
    );
  };

  const ListingDetailsScreen = () => {
    // Mock listing data for details
    const listing = {
      title: "Spacious Apartment in Amsterdam",
      price: "€1,800/month",
      location: "Amsterdam-Zuid",
      bedrooms: 2,
      bathrooms: 1,
      size: "85 sqm",
      description:
        "A beautiful, modern apartment located in the vibrant Amsterdam-Zuid area. Features two spacious bedrooms, a large living area, and a fully equipped kitchen. Close to public transport, parks, and amenities. Perfect for young professionals or a small family.",
      features: ["Balcony", "Washing Machine", "Furnished", "Central Heating"],
      imageUrl:
        "https://placehold.co/600x400/e0e0e0/333333?text=Apartment+Details",
    };

    return (
      <div className="flex flex-col h-full bg-white rounded-2xl">
        <Header
          title="Listing Details"
          showBackButton={true}
          onBack={() => navigateTo("Dashboard")}
        />
        <div className="p-6 flex-grow overflow-y-auto">
          <img
            src={listing.imageUrl}
            alt={listing.title}
            className="w-full h-56 object-cover rounded-xl mb-6"
          />
          <h1 className="text-2xl font-bold text-gray-800 mb-2">
            {listing.title}
          </h1>
          <p className="text-pink-600 font-bold text-2xl mb-4">
            {listing.price}
          </p>

          <div className="grid grid-cols-2 gap-4 text-gray-700 mb-6">
            <p>
              <i className="fas fa-map-marker-alt mr-2 text-gray-500"></i>
              {listing.location}
            </p>
            <p>
              <i className="fas fa-bed mr-2 text-gray-500"></i>
              {listing.bedrooms} Bedrooms
            </p>
            <p>
              <i className="fas fa-bath mr-2 text-gray-500"></i>
              {listing.bathrooms} Bathroom
            </p>
            <p>
              <i className="fas fa-ruler-combined mr-2 text-gray-500"></i>
              {listing.size}
            </p>
          </div>

          <h2 className="text-xl font-semibold text-gray-800 mb-3">
            Description
          </h2>
          <p className="text-gray-600 mb-6 leading-relaxed">
            {listing.description}
          </p>

          <h2 className="text-xl font-semibold text-gray-800 mb-3">Features</h2>
          <ul className="list-disc list-inside text-gray-600 mb-8">
            {listing.features.map((feature, index) => (
              <li key={index}>{feature}</li>
            ))}
          </ul>

          <button
            onClick={() => navigateTo("Application")}
            className="w-full bg-pink-600 text-white py-3 px-6 rounded-xl font-semibold text-lg hover:bg-pink-700 transition duration-300 flex items-center justify-center"
          >
            Apply for this Property
          </button>
        </div>
      </div>
    );
  };

  const ApplicationScreen = () => {
    const [applicationText, setApplicationText] = useState("");
    const [isLoadingAI, setIsLoadingAI] = useState(false);

    const generateApplication = async () => {
      setIsLoadingAI(true);
      setApplicationText("Generating your personalized application letter...");

      // Simulate API call to AI layer
      try {
        const prompt =
          "Generate a professional rental application letter for a young professional applying for an apartment in Amsterdam. Highlight reliability, good financial standing, and quiet lifestyle.";
        const apiKey = ""; // Canvas will provide this if needed for gemini-2.0-flash
        const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${apiKey}`;

        const payload = {
          contents: [{ role: "user", parts: [{ text: prompt }] }],
        };

        const response = await fetch(apiUrl, {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(payload),
        });

        const result = await response.json();
        if (
          result.candidates &&
          result.candidates.length > 0 &&
          result.candidates[0].content &&
          result.candidates[0].content.parts &&
          result.candidates[0].content.parts.length > 0
        ) {
          setApplicationText(result.candidates[0].content.parts[0].text);
        } else {
          setApplicationText(
            "Failed to generate application. Please try again."
          );
          console.error("AI response structure unexpected:", result);
        }
      } catch (error) {
        console.error("Error calling AI API:", error);
        setApplicationText(
          "Error generating application. Please check your connection."
        );
      } finally {
        setIsLoadingAI(false);
      }
    };

    const sendApplication = () => {
      // Simulate sending application
      // Using a custom modal for simplicity instead of alert()
      const modal = document.createElement("div");
      modal.className =
        "fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50";
      modal.innerHTML = `
                <div class="bg-white p-6 rounded-xl shadow-lg text-center">
                    <p class="text-lg font-semibold mb-4">Application Sent!</p>
                    <button id="modal-close" class="bg-pink-600 text-white py-2 px-4 rounded-lg">Close</button>
                </div>
            `;
      document.body.appendChild(modal);
      document.getElementById("modal-close").onclick = () =>
        document.body.removeChild(modal);
    };

    return (
      <div className="flex flex-col h-full bg-white rounded-2xl">
        <Header
          title="Rental Application"
          showBackButton={true}
          onBack={() => navigateTo("Dashboard")}
        />
        <div className="p-6 flex-grow overflow-y-auto">
          <h1 className="text-2xl font-semibold text-gray-800 mb-4 mt-4">
            Generate Your Application
          </h1>
          <p className="text-gray-600 mb-6 leading-relaxed">
            Let our AI draft a personalized application letter for you.
          </p>

          <button
            onClick={generateApplication}
            className="w-full bg-pink-600 text-white py-3 px-6 rounded-xl font-semibold text-lg hover:bg-pink-700 transition duration-300 mb-6 flex items-center justify-center"
            disabled={!contractText || isLoadingAI}
          >
            {isLoadingAI ? (
              <svg
                className="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
              >
                <circle
                  className="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  strokeWidth="4"
                ></circle>
                <path
                  className="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                ></path>
              </svg>
            ) : (
              <>
                Generate Letter <span className="ml-2">&rarr;</span>
              </>
            )}
          </button>

          <textarea
            className="w-full h-64 p-4 border border-gray-200 rounded-xl font-inter text-gray-700 focus:border-pink-600 outline-none resize-none mb-6"
            placeholder="Your AI-generated application letter will appear here..."
            value={applicationText}
            onChange={(e) => setApplicationText(e.target.value)}
          ></textarea>

          <button
            onClick={sendApplication}
            className="w-full bg-green-600 text-white py-3 px-6 rounded-xl font-semibold text-lg hover:bg-green-700 transition duration-300 flex items-center justify-center"
            disabled={!applicationText || isLoadingAI}
          >
            Send Application
          </button>
        </div>
      </div>
    );
  };

  const ContractReviewScreen = () => {
    const [contractText, setContractText] = useState("");
    const [analysisResult, setAnalysisResult] = useState("");
    const [isLoadingAI, setIsLoadingAI] = useState(false);

    const analyzeContract = async () => {
      setIsLoadingAI(true);
      setAnalysisResult("Analyzing your contract for legal risks...");

      // Simulate API call to AI layer
      try {
        const prompt = `Analyze the following rental contract text for potential legal risks, unfair clauses, or important points for a tenant in the Netherlands. Summarize key terms. Contract: "${contractText}"`;
        const apiKey = ""; // Canvas will provide this if needed for gemini-2.0-flash
        const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${apiKey}`;

        const payload = {
          contents: [{ role: "user", parts: [{ text: prompt }] }],
        };

        const response = await fetch(apiUrl, {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(payload),
        });

        const result = await response.json();
        if (
          result.candidates &&
          result.candidates.length > 0 &&
          result.candidates[0].content &&
          result.candidates[0].content.parts &&
          result.candidates[0].content.parts.length > 0
        ) {
          setAnalysisResult(result.candidates[0].content.parts[0].text);
        } else {
          setAnalysisResult("Failed to analyze contract. Please try again.");
          console.error("AI response structure unexpected:", result);
        }
      } catch (error) {
        console.error("Error calling AI API:", error);
        setAnalysisResult(
          "Error analyzing contract. Please check your connection."
        );
      } finally {
        setIsLoadingAI(false);
      }
    };

    return (
      <div className="flex flex-col h-full bg-white rounded-2xl">
        <Header
          title="Contract Review"
          showBackButton={true}
          onBack={() => navigateTo("Dashboard")}
        />
        <div className="p-6 flex-grow overflow-y-auto">
          <h1 className="text-2xl font-semibold text-gray-800 mb-4 mt-4">
            AI Contract Analysis
          </h1>
          <p className="text-gray-600 mb-6 leading-relaxed">
            Paste your rental contract text below for an AI-powered legal
            review.
          </p>

          <textarea
            className="w-full h-64 p-4 border border-gray-200 rounded-xl font-inter text-gray-700 focus:border-pink-600 outline-none resize-none mb-6"
            placeholder="Paste your contract text here..."
            value={contractText}
            onChange={(e) => setContractText(e.target.value)}
          ></textarea>

          <button
            onClick={analyzeContract}
            className="w-full bg-pink-600 text-white py-3 px-6 rounded-xl font-semibold text-lg hover:bg-pink-700 transition duration-300 mb-6 flex items-center justify-center"
            disabled={!contractText || isLoadingAI}
          >
            {isLoadingAI ? (
              <svg
                className="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
              >
                <circle
                  className="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  strokeWidth="4"
                ></circle>
                <path
                  className="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                ></path>
              </svg>
            ) : (
              <>
                Analyze Contract <span className="ml-2">&rarr;</span>
              </>
            )}
          </button>

          {analysisResult && (
            <div className="bg-gray-50 border border-gray-200 p-4 rounded-xl text-left">
              <h3 className="text-lg font-semibold text-gray-800 mb-2">
                Analysis Result:
              </h3>
              <p className="text-gray-700 whitespace-pre-wrap">
                {analysisResult}
              </p>
            </div>
          )}
        </div>
      </div>
    );
  };

  // Render the current screen based on state
  const renderScreen = () => {
    switch (currentScreen) {
      case "Welcome":
        return <WelcomeScreen />;
      case "LoginSignUp":
        return <LoginSignUpScreen />;
      case "PreferencesSetup":
        return <PreferencesSetupScreen />;
      case "Dashboard":
        return <DashboardScreen />;
      case "ListingDetails":
        return <ListingDetailsScreen />;
      case "Application":
        return <ApplicationScreen />;
      case "ContractReview":
        return <ContractReviewScreen />;
      default:
        return <WelcomeScreen />;
    }
  };

  return (
    <div className="min-h-screen bg-gray-200 flex items-center justify-center p-4">
      <div className="relative w-full h-[800px] max-w-md bg-white rounded-3xl shadow-xl overflow-hidden flex flex-col">
        {renderScreen()}
      </div>
    </div>
  );
};

export default App;
